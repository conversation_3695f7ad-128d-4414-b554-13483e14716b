#!/usr/bin/env python
"""
Script để cập nhật tên trong User accounts cho khớp với tên tiếng Việt của sinh viên
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from django.db import transaction

def update_user_names():
    """Cập nhật tên trong User accounts"""
    print("📝 Đang cập nhật tên trong User accounts...")
    
    updated_count = 0
    not_found_count = 0
    
    for student in Student.objects.all():
        try:
            user = User.objects.get(email=student.email)
            
            # Cập nhật tên
            user.first_name = student.first_name
            user.last_name = student.last_name
            user.save()
            
            updated_count += 1
            
            if updated_count % 10 == 0:
                print(f"   ✓ Đã cập nhật {updated_count} user accounts...")
                
        except User.DoesNotExist:
            print(f"   ⚠️  Không tìm thấy user cho sinh viên: {student.full_name()}")
            not_found_count += 1
            continue
    
    print(f"✅ Đã cập nhật {updated_count} user accounts")
    if not_found_count > 0:
        print(f"⚠️  {not_found_count} sinh viên không có user account")
    
    return updated_count

def verify_user_names():
    """Kiểm tra tên trong User accounts"""
    print("\n🔍 KIỂM TRA TÊN TRONG USER ACCOUNTS")
    print("-" * 50)
    
    sample_users = User.objects.filter(is_staff=False, is_superuser=False)[:10]
    
    print(f"{'Username':<12} {'Họ tên trong User':<25} {'Họ tên sinh viên':<25}")
    print("-" * 70)
    
    for user in sample_users:
        user_full_name = f"{user.last_name} {user.first_name}" if user.last_name and user.first_name else "N/A"
        
        try:
            student = Student.objects.get(email=user.email)
            student_full_name = student.full_name()
            
            if user_full_name == student_full_name:
                status = "✅"
            else:
                status = "❌"
            
            print(f"{user.username:<12} {user_full_name:<25} {student_full_name:<25} {status}")
            
        except Student.DoesNotExist:
            print(f"{user.username:<12} {user_full_name:<25} {'Không có sinh viên':<25} ❌")

def main():
    """Hàm chính"""
    print("🎯 CẬP NHẬT TÊN TRONG USER ACCOUNTS")
    print("=" * 50)
    
    try:
        with transaction.atomic():
            # Cập nhật tên
            update_user_names()
            
            # Kiểm tra kết quả
            verify_user_names()
            
            print("\n🎉 HOÀN THÀNH CẬP NHẬT!")
            print("=" * 50)
            print("✅ Tên trong User accounts đã được cập nhật!")
            print("🌐 Sinh viên có thể đăng nhập với tên tiếng Việt")
            
    except Exception as e:
        print(f"❌ Lỗi khi cập nhật: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
