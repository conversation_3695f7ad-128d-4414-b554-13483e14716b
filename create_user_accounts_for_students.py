#!/usr/bin/env python
"""
Script để tạo User accounts cho các sinh viên tiếng Việt
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from django.db import transaction

def create_user_accounts_for_students():
    """Tạo User accounts cho các sinh viên chưa có account"""
    print("👤 Đang tạo User accounts cho sinh viên...")
    
    students = Student.objects.all()
    created_count = 0
    existing_count = 0
    
    for student in students:
        # Kiểm tra xem đã có User account chưa
        if User.objects.filter(email=student.email).exists():
            existing_count += 1
            continue
        
        try:
            # Tạo User account mới
            user = User.objects.create_user(
                username=student.student_id,
                email=student.email,
                password='student123',  # Password mặc định
                first_name=student.first_name,
                last_name=student.last_name,
                is_staff=False,
                is_active=True
            )
            created_count += 1
            
            if created_count % 10 == 0:
                print(f"   ✓ Đã tạo {created_count} user accounts...")
                
        except Exception as e:
            print(f"   ⚠️  Lỗi khi tạo user cho {student.full_name()}: {e}")
            continue
    
    print(f"✅ Đã tạo {created_count} user accounts mới")
    print(f"📋 {existing_count} sinh viên đã có user accounts")
    return created_count

def clean_old_user_accounts():
    """Xóa các User accounts cũ không còn sinh viên tương ứng"""
    print("\n🧹 Đang dọn dẹp User accounts cũ...")
    
    # Lấy danh sách email của sinh viên hiện tại
    current_student_emails = set(Student.objects.values_list('email', flat=True))
    
    # Tìm các User có email không thuộc sinh viên hiện tại (trừ admin)
    old_users = User.objects.exclude(email__in=current_student_emails).exclude(is_superuser=True)
    
    # Giữ lại một số user quan trọng
    important_users = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
    old_users = old_users.exclude(email__in=important_users)
    
    deleted_count = old_users.count()
    
    if deleted_count > 0:
        print(f"   🗑️  Sẽ xóa {deleted_count} user accounts cũ...")
        old_users.delete()
        print(f"   ✅ Đã xóa {deleted_count} user accounts cũ")
    else:
        print("   ✅ Không có user accounts cũ nào cần xóa")
    
    return deleted_count

def print_user_statistics():
    """In thống kê User accounts"""
    print("\n📊 THỐNG KÊ USER ACCOUNTS:")
    print("-" * 50)
    
    total_users = User.objects.count()
    admin_users = User.objects.filter(is_superuser=True).count()
    staff_users = User.objects.filter(is_staff=True, is_superuser=False).count()
    student_users = User.objects.filter(is_staff=False, is_superuser=False).count()
    
    print(f"   👥 Tổng số users: {total_users}")
    print(f"   👑 Admin users: {admin_users}")
    print(f"   👨‍💼 Staff users: {staff_users}")
    print(f"   🎓 Student users: {student_users}")
    
    # Kiểm tra sinh viên có user account
    students_with_users = 0
    students_without_users = 0
    
    for student in Student.objects.all():
        if User.objects.filter(email=student.email).exists():
            students_with_users += 1
        else:
            students_without_users += 1
    
    print(f"   ✅ Sinh viên có user account: {students_with_users}")
    print(f"   ❌ Sinh viên chưa có user account: {students_without_users}")

def print_sample_accounts():
    """In một số user accounts mẫu"""
    print("\n📋 MỘT SỐ USER ACCOUNTS MẪU:")
    print("-" * 80)
    
    # Lấy một số student users
    student_users = User.objects.filter(is_staff=False, is_superuser=False)[:10]
    
    print(f"{'Username':<15} {'Email':<35} {'Họ tên':<25}")
    print("-" * 80)
    
    for user in student_users:
        full_name = f"{user.last_name} {user.first_name}" if user.last_name and user.first_name else "N/A"
        print(f"{user.username:<15} {user.email:<35} {full_name:<25}")
    
    if student_users.count() > 10:
        print(f"... và {student_users.count() - 10} user accounts khác")

def test_login_functionality():
    """Test thử chức năng đăng nhập"""
    print("\n🔐 TEST CHỨC NĂNG ĐĂNG NHẬP:")
    print("-" * 50)
    
    # Lấy một sinh viên ngẫu nhiên
    sample_student = Student.objects.first()
    if sample_student:
        try:
            user = User.objects.get(email=sample_student.email)
            print(f"   ✅ Tìm thấy user cho sinh viên: {sample_student.full_name()}")
            print(f"   📧 Email: {user.email}")
            print(f"   👤 Username: {user.username}")
            print(f"   🔑 Password mặc định: student123")
            print(f"   🌐 Có thể đăng nhập tại: http://127.0.0.1:8000/login/")
        except User.DoesNotExist:
            print(f"   ❌ Không tìm thấy user cho sinh viên: {sample_student.full_name()}")
    else:
        print("   ❌ Không có sinh viên nào trong hệ thống")

def main():
    """Hàm chính"""
    print("🎯 TẠO USER ACCOUNTS CHO SINH VIÊN TIẾNG VIỆT")
    print("=" * 60)
    
    try:
        with transaction.atomic():
            # Bước 1: Dọn dẹp user accounts cũ
            clean_old_user_accounts()
            
            # Bước 2: Tạo user accounts cho sinh viên mới
            create_user_accounts_for_students()
            
            # Bước 3: In thống kê
            print_user_statistics()
            print_sample_accounts()
            test_login_functionality()
            
            print("\n🎉 HOÀN THÀNH TẠO USER ACCOUNTS!")
            print("=" * 60)
            print("✅ User accounts đã được tạo cho tất cả sinh viên!")
            print("🔑 Password mặc định cho tất cả sinh viên: student123")
            print("🌐 Sinh viên có thể đăng nhập tại: http://127.0.0.1:8000/login/")
            print("📝 Username: mã sinh viên (ví dụ: ********)")
            print("🔐 Password: student123")
            
    except Exception as e:
        print(f"❌ Lỗi khi tạo user accounts: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
