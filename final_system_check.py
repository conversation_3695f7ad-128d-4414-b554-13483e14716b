#!/usr/bin/env python
"""
Script kiểm tra tổng thể hệ thống sau khi reset sinh viên
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from courses.models import Course, Enrollment
from django.db.models import Avg, Count

def print_system_overview():
    """In tổng quan hệ thống"""
    print("🎯 TỔNG QUAN HỆ THỐNG QUẢN LÝ SINH VIÊN")
    print("=" * 60)
    
    # Thống kê cơ bản
    total_students = Student.objects.count()
    total_courses = Course.objects.count()
    total_enrollments = Enrollment.objects.count()
    total_users = User.objects.count()
    
    print(f"👥 Tổng số sinh viên: {total_students}")
    print(f"📚 Tổng số môn học: {total_courses}")
    print(f"📝 Tổng số đăng ký: {total_enrollments}")
    print(f"👤 Tổng số user accounts: {total_users}")

def check_vietnamese_names():
    """Kiểm tra tên tiếng Việt"""
    print("\n🇻🇳 KIỂM TRA TÊN TIẾNG VIỆT")
    print("-" * 40)
    
    students = Student.objects.all()
    vietnamese_names = 0
    
    # Danh sách họ tiếng Việt để kiểm tra
    vietnamese_surnames = [
        "Nguyễn", "Trần", "Lê", "Phạm", "Hoàng", "Huỳnh", "Phan", "Vũ", "Võ", "Đặng",
        "Bùi", "Đỗ", "Hồ", "Ngô", "Dương", "Lý", "Đinh", "Đào", "Lương", "Lâm",
        "Tạ", "Vương", "Tô", "Chu", "Triệu", "Nông", "Lưu", "Thái", "Cao", "Kiều",
        "Ông", "Đoàn", "Tăng", "Thạch", "Hà", "Viên", "Trang", "Khương", "Bạch", "Mã"
    ]
    
    for student in students:
        if student.last_name in vietnamese_surnames:
            vietnamese_names += 1
    
    percentage = (vietnamese_names / students.count()) * 100 if students.count() > 0 else 0
    
    print(f"✅ Sinh viên có họ tiếng Việt: {vietnamese_names}/{students.count()} ({percentage:.1f}%)")
    
    # Hiển thị một số tên mẫu
    print("\n📋 Một số tên sinh viên mẫu:")
    sample_students = students[:8]
    for student in sample_students:
        print(f"   - {student.full_name()}")

def check_user_accounts():
    """Kiểm tra user accounts"""
    print("\n👤 KIỂM TRA USER ACCOUNTS")
    print("-" * 30)
    
    total_users = User.objects.count()
    admin_users = User.objects.filter(is_superuser=True).count()
    student_users = User.objects.filter(is_staff=False, is_superuser=False).count()
    
    print(f"👑 Admin users: {admin_users}")
    print(f"🎓 Student users: {student_users}")
    print(f"📊 Tổng users: {total_users}")
    
    # Kiểm tra sinh viên có user account
    students_with_accounts = 0
    for student in Student.objects.all():
        if User.objects.filter(email=student.email).exists():
            students_with_accounts += 1
    
    print(f"✅ Sinh viên có user account: {students_with_accounts}/{Student.objects.count()}")

def check_enrollments():
    """Kiểm tra đăng ký môn học"""
    print("\n📝 KIỂM TRA ĐĂNG KÝ MÔN HỌC")
    print("-" * 35)
    
    total_enrollments = Enrollment.objects.count()
    graded_enrollments = Enrollment.objects.filter(grade__isnull=False).count()
    ungraded_enrollments = total_enrollments - graded_enrollments
    
    print(f"📝 Tổng đăng ký: {total_enrollments}")
    print(f"✅ Đã có điểm: {graded_enrollments}")
    print(f"⏳ Chưa có điểm: {ungraded_enrollments}")
    
    if graded_enrollments > 0:
        avg_grade = Enrollment.objects.filter(numeric_grade__isnull=False).aggregate(
            avg=Avg('numeric_grade')
        )['avg']
        print(f"📈 Điểm trung bình: {avg_grade:.2f}")
    
    # Phân bố điểm
    grade_distribution = {}
    for grade in ['A', 'B', 'C', 'D', 'F']:
        count = Enrollment.objects.filter(grade=grade).count()
        if count > 0:
            grade_distribution[grade] = count
    
    if grade_distribution:
        print("\n📊 Phân bố điểm:")
        for grade, count in grade_distribution.items():
            percentage = (count / graded_enrollments) * 100
            print(f"   {grade}: {count} ({percentage:.1f}%)")

def check_top_students():
    """Kiểm tra sinh viên xuất sắc"""
    print("\n🏆 TOP 5 SINH VIÊN XUẤT SẮC")
    print("-" * 35)
    
    top_students = Student.objects.annotate(
        avg_grade=Avg('enrollments__numeric_grade'),
        total_courses=Count('enrollments')
    ).filter(avg_grade__isnull=False).order_by('-avg_grade')[:5]
    
    for i, student in enumerate(top_students, 1):
        print(f"   {i}. {student.full_name()} - Điểm TB: {student.avg_grade:.2f} ({student.total_courses} môn)")

def print_login_guide():
    """In hướng dẫn đăng nhập"""
    print("\n📝 HƯỚNG DẪN SỬ DỤNG HỆ THỐNG")
    print("=" * 40)
    
    print("🌐 URL hệ thống: http://127.0.0.1:8000")
    print("🔐 URL đăng nhập: http://127.0.0.1:8000/login/")
    
    print("\n👥 Tài khoản sinh viên mẫu:")
    sample_students = Student.objects.all()[:3]
    for student in sample_students:
        print(f"   Username: {student.student_id}")
        print(f"   Password: student123")
        print(f"   Họ tên: {student.full_name()}")
        print(f"   Email: {student.email}")
        print()
    
    print("📋 Tính năng sinh viên có thể sử dụng:")
    print("   📚 Môn học của tôi - xem tất cả môn đã đăng ký")
    print("   👤 Thông tin cá nhân - xem và chỉnh sửa thông tin")
    print("   📊 Xem điểm số và thống kê học tập")
    print("   ⚙️  Quản lý đăng ký môn học")
    
    print("\n🎯 Tính năng đã cải tiến:")
    print("   ✅ Clickable cards - click vào card để xem chi tiết")
    print("   ✅ Navigation cải tiến - dễ dàng chuyển đổi giữa các trang")
    print("   ✅ Responsive design - tương thích mobile")
    print("   ✅ Hover effects và animations")

def main():
    """Hàm chính"""
    try:
        print_system_overview()
        check_vietnamese_names()
        check_user_accounts()
        check_enrollments()
        check_top_students()
        print_login_guide()
        
        print("\n🎉 HỆ THỐNG ĐÃ SẴN SÀNG!")
        print("=" * 40)
        print("✅ Tất cả dữ liệu đã được reset thành công!")
        print("✅ 100 sinh viên với tên tiếng Việt không trùng lặp!")
        print("✅ User accounts đã được tạo và cập nhật!")
        print("✅ Dữ liệu đăng ký môn học đã được tạo!")
        print("✅ Tất cả tính năng đã cải tiến hoạt động bình thường!")
        print("\n🌐 Truy cập http://127.0.0.1:8000 để sử dụng hệ thống!")
        
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra hệ thống: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
