#!/usr/bin/env python
"""
Script để xóa toàn bộ sinh viên và tạo lại với tên tiếng Việt không trùng lặp
"""
import os
import sys
import django
import random
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from students.models import Student
from courses.models import Enrollment
from django.db import transaction

# Danh sách họ tiếng Việt phổ biến
VIETNAMESE_LAST_NAMES = [
    "<PERSON><PERSON>ễn", "<PERSON>r<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Đặng",
    "<PERSON><PERSON><PERSON>", "Đỗ", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
    "Ông", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>ng", "<PERSON>h<PERSON>ch", "<PERSON><PERSON>", "<PERSON>i<PERSON>n", "<PERSON>rang", "<PERSON>h<PERSON><PERSON>ng", "<PERSON><PERSON>ch", "<PERSON><PERSON>",
    "<PERSON>u<PERSON>ch", "<PERSON><PERSON>n", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>a", "<PERSON><PERSON>c", "<PERSON><PERSON><PERSON>", "Ứng", "<PERSON>h<PERSON>ng", "<PERSON>r<PERSON><PERSON>", "<PERSON>"
]

# <PERSON>h s<PERSON>ch t<PERSON>n ti<PERSON>ng <PERSON>i<PERSON>t cho nam
VIETNAMESE_MALE_NAMES = [
    "Anh", "An", "Bảo", "Bình", "Cường", "Dũng", "Đức", "Đạt", "Giang", "Hải",
    "Hùng", "Hưng", "Khang", "Kiên", "Long", "Minh", "Nam", "Phong", "Quang", "Sơn",
    "Thành", "Thắng", "Thiện", "Tuấn", "Tùng", "Việt", "Vinh", "Vũ", "Xuân", "Yên",
    "Hoàng", "Huy", "Khánh", "Lâm", "Linh", "Lộc", "Mạnh", "Nghĩa", "Nhân", "Phúc",
    "Quân", "Tài", "Thái", "Thịnh", "Tiến", "Toàn", "Trí", "Trung", "Tú", "Văn",
    "Đăng", "Đình", "Gia", "Hà", "Hiếu", "Hoài", "Khôi", "Lợi", "Ngọc", "Phát",
    "Quốc", "Tâm", "Thanh", "Thiên", "Thông", "Thuận", "Tiến", "Trọng", "Vĩnh", "Vương"
]

# Danh sách tên tiếng Việt cho nữ
VIETNAMESE_FEMALE_NAMES = [
    "Anh", "Ánh", "Bảo", "Chi", "Diệu", "Giang", "Hà", "Hạnh", "Hiền", "Hoa",
    "Hương", "Khánh", "Lan", "Linh", "Mai", "Minh", "My", "Nga", "Ngọc", "Nhung",
    "Phương", "Quỳnh", "Thảo", "Thúy", "Trang", "Trinh", "Tú", "Tuyết", "Uyên", "Vân",
    "Yến", "Ý", "Bích", "Cẩm", "Diễm", "Hằng", "Huyền", "Kim", "Lệ", "Ly",
    "Mỹ", "Nhi", "Oanh", "Phúc", "Quyên", "Thùy", "Tiên", "Trâm", "Uyển", "Vũ",
    "Xuân", "Yên", "An", "Châu", "Dung", "Hạ", "Hồng", "Kiều", "Lam", "Liên",
    "Nhan", "Như", "Phượng", "Quế", "Thu", "Thư", "Tịnh", "Trúc", "Vi", "Yến"
]

# Danh sách tỉnh thành Việt Nam
VIETNAMESE_PROVINCES = [
    "An Giang", "Bà Rịa - Vũng Tàu", "Bắc Giang", "Bắc Kạn", "Bạc Liêu", "Bắc Ninh",
    "Bến Tre", "Bình Định", "Bình Dương", "Bình Phước", "Bình Thuận", "Cà Mau",
    "Cao Bằng", "Đắk Lắk", "Đắk Nông", "Điện Biên", "Đồng Nai", "Đồng Tháp",
    "Gia Lai", "Hà Giang", "Hà Nam", "Hà Tĩnh", "Hải Dương", "Hậu Giang",
    "Hòa Bình", "Hưng Yên", "Khánh Hòa", "Kiên Giang", "Kon Tum", "Lai Châu",
    "Lâm Đồng", "Lạng Sơn", "Lào Cai", "Long An", "Nam Định", "Nghệ An",
    "Ninh Bình", "Ninh Thuận", "Phú Thọ", "Phú Yên", "Quảng Bình", "Quảng Nam",
    "Quảng Ngãi", "Quảng Ninh", "Quảng Trị", "Sóc Trăng", "Sơn La", "Tây Ninh",
    "Thái Bình", "Thái Nguyên", "Thanh Hóa", "Thừa Thiên Huế", "Tiền Giang",
    "Trà Vinh", "Tuyên Quang", "Vĩnh Long", "Vĩnh Phúc", "Yên Bái",
    "Hà Nội", "TP. Hồ Chí Minh", "Đà Nẵng", "Hải Phòng", "Cần Thơ"
]

def generate_student_id(index):
    """Tạo mã sinh viên theo format năm + số thứ tự"""
    year = random.choice([2020, 2021, 2022, 2023, 2024])
    return f"{year}{index:04d}"

def generate_phone():
    """Tạo số điện thoại Việt Nam"""
    prefixes = ['090', '091', '094', '083', '084', '085', '081', '082', '032', '033', '034', '035', '036', '037', '038', '039']
    return f"{random.choice(prefixes)}{random.randint(1000000, 9999999)}"

def generate_vietnamese_address():
    """Tạo địa chỉ tiếng Việt"""
    street_number = random.randint(1, 999)
    street_names = [
        "Lê Lợi", "Nguyễn Huệ", "Trần Hưng Đạo", "Hai Bà Trưng", "Lý Thường Kiệt",
        "Điện Biên Phủ", "Võ Thị Sáu", "Nguyễn Thị Minh Khai", "Lê Duẩn", "Hoàng Văn Thụ",
        "Phan Chu Trinh", "Nguyễn Du", "Lê Thánh Tôn", "Pasteur", "Cách Mạng Tháng Tám",
        "Nguyễn Trãi", "Quang Trung", "Lê Văn Sỹ", "Cộng Hòa", "Hoàng Hoa Thám"
    ]
    ward_names = [
        "Phường 1", "Phường 2", "Phường 3", "Phường Bến Nghé", "Phường Đa Kao",
        "Phường Tân Định", "Phường Nguyễn Thái Bình", "Phường Phạm Ngũ Lão",
        "Phường Cầu Ông Lãnh", "Phường Cô Giang", "Phường Nguyễn Cư Trinh"
    ]
    district_names = [
        "Quận 1", "Quận 2", "Quận 3", "Quận 4", "Quận 5", "Quận 6", "Quận 7",
        "Quận 8", "Quận 9", "Quận 10", "Quận 11", "Quận 12", "Quận Bình Thạnh",
        "Quận Gò Vấp", "Quận Phú Nhuận", "Quận Tân Bình", "Quận Tân Phú"
    ]
    
    street = f"{street_number} {random.choice(street_names)}"
    ward = random.choice(ward_names)
    district = random.choice(district_names)
    province = random.choice(VIETNAMESE_PROVINCES)
    
    return f"{street}, {ward}, {district}, {province}"

def generate_birth_date():
    """Tạo ngày sinh hợp lý cho sinh viên (18-25 tuổi)"""
    start_date = datetime.now() - timedelta(days=25*365)  # 25 tuổi
    end_date = datetime.now() - timedelta(days=18*365)    # 18 tuổi
    
    time_between = end_date - start_date
    days_between = time_between.days
    random_days = random.randrange(days_between)
    
    return start_date + timedelta(days=random_days)

def delete_all_students():
    """Xóa toàn bộ sinh viên và enrollment liên quan"""
    print("🗑️  Đang xóa toàn bộ sinh viên hiện tại...")
    
    # Xóa tất cả enrollment trước
    enrollment_count = Enrollment.objects.count()
    Enrollment.objects.all().delete()
    print(f"   ✓ Đã xóa {enrollment_count} đăng ký môn học")
    
    # Xóa tất cả sinh viên
    student_count = Student.objects.count()
    Student.objects.all().delete()
    print(f"   ✓ Đã xóa {student_count} sinh viên")

def create_vietnamese_students(count=100):
    """Tạo sinh viên với tên tiếng Việt không trùng lặp"""
    print(f"👥 Đang tạo {count} sinh viên với tên tiếng Việt...")
    
    used_names = set()  # Để tránh trùng lặp tên
    used_emails = set()  # Để tránh trùng lặp email
    used_student_ids = set()  # Để tránh trùng lặp mã sinh viên
    
    created_count = 0
    attempts = 0
    max_attempts = count * 3  # Giới hạn số lần thử để tránh vòng lặp vô hạn
    
    while created_count < count and attempts < max_attempts:
        attempts += 1
        
        # Tạo tên không trùng lặp
        last_name = random.choice(VIETNAMESE_LAST_NAMES)
        gender = random.choice(['M', 'F'])
        
        if gender == 'M':
            first_name = random.choice(VIETNAMESE_MALE_NAMES)
        else:
            first_name = random.choice(VIETNAMESE_FEMALE_NAMES)
        
        full_name = f"{last_name} {first_name}"
        
        # Kiểm tra trùng lặp tên
        if full_name in used_names:
            continue
        
        # Tạo mã sinh viên không trùng lặp
        student_id = generate_student_id(created_count + 1)
        while student_id in used_student_ids:
            student_id = generate_student_id(random.randint(1, 9999))
        
        # Tạo email không trùng lặp
        email = f"{student_id}@student.edu.vn"
        if email in used_emails:
            continue
        
        try:
            student = Student.objects.create(
                student_id=student_id,
                first_name=first_name,
                last_name=last_name,
                date_of_birth=generate_birth_date().date(),
                gender=gender,
                email=email,
                phone_number=generate_phone(),
                address=generate_vietnamese_address(),
                is_active=random.choice([True, True, True, True, False])  # 80% active
            )
            
            # Thêm vào set để tránh trùng lặp
            used_names.add(full_name)
            used_emails.add(email)
            used_student_ids.add(student_id)
            
            created_count += 1
            
            if created_count % 10 == 0:
                print(f"   ✓ Đã tạo {created_count}/{count} sinh viên...")
                
        except Exception as e:
            print(f"   ⚠️  Lỗi khi tạo sinh viên: {e}")
            continue
    
    print(f"✅ Đã tạo thành công {created_count} sinh viên với tên tiếng Việt không trùng lặp")
    return Student.objects.all()

def print_sample_students():
    """In ra một số sinh viên mẫu để kiểm tra"""
    print("\n📋 Danh sách một số sinh viên mẫu:")
    print("-" * 80)
    students = Student.objects.all()[:10]
    
    for student in students:
        print(f"   {student.student_id} | {student.full_name()} | {student.get_gender_display()} | {student.email}")
    
    if students.count() > 10:
        print(f"   ... và {students.count() - 10} sinh viên khác")

def main():
    """Hàm chính"""
    print("🎯 BẮT ĐẦU RESET SINH VIÊN VỚI TÊN TIẾNG VIỆT")
    print("=" * 60)
    
    try:
        with transaction.atomic():
            # Bước 1: Xóa toàn bộ sinh viên hiện tại
            delete_all_students()
            
            # Bước 2: Tạo sinh viên mới với tên tiếng Việt
            students = create_vietnamese_students(100)  # Tạo 100 sinh viên
            
            # Bước 3: In thống kê
            print_sample_students()
            
            print(f"\n📊 THỐNG KÊ:")
            print(f"   👥 Tổng số sinh viên: {Student.objects.count()}")
            print(f"   👨 Sinh viên nam: {Student.objects.filter(gender='M').count()}")
            print(f"   👩 Sinh viên nữ: {Student.objects.filter(gender='F').count()}")
            print(f"   ✅ Sinh viên đang học: {Student.objects.filter(is_active=True).count()}")
            print(f"   ❌ Sinh viên đã nghỉ: {Student.objects.filter(is_active=False).count()}")
            
            print("\n🎉 HOÀN THÀNH RESET SINH VIÊN!")
            print("=" * 60)
            print("✅ Dữ liệu sinh viên đã được reset với tên tiếng Việt không trùng lặp!")
            print("🌐 Bạn có thể truy cập http://127.0.0.1:8000 để xem kết quả")
            
    except Exception as e:
        print(f"❌ Lỗi khi reset dữ liệu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
