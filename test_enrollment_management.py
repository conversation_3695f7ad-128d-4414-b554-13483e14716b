#!/usr/bin/env python
"""
Script để test tính năng quản lý đăng ký môn học
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from courses.models import Course, Enrollment
from django.test import Client
from django.urls import reverse

def test_enrollment_management():
    """Test tính năng quản lý đăng ký"""
    print("🎯 TEST TÍNH NĂNG QUẢN LÝ ĐĂNG KÝ MÔN HỌC")
    print("=" * 60)
    
    # Lấy một sinh viên để test
    student = Student.objects.first()
    if not student:
        print("❌ Không có sinh viên nào để test")
        return
    
    try:
        user = User.objects.get(email=student.email)
    except User.DoesNotExist:
        print(f"❌ Không tìm thấy user account cho sinh viên {student.full_name()}")
        return
    
    print(f"🎓 Test với sinh viên: {student.full_name()}")
    print(f"📧 Email: {student.email}")
    
    # Tạo client để test
    client = Client()
    
    # Đăng nhập
    login_success = client.login(username=user.username, password='student123')
    if not login_success:
        print("❌ Đăng nhập thất bại")
        return
    
    print("✅ Đăng nhập thành công")
    
    # Test truy cập trang quản lý đăng ký
    print("\n📋 Test trang quản lý đăng ký...")
    try:
        response = client.get(reverse('my-enrollment-management'))
        if response.status_code == 200:
            print("   ✅ Truy cập trang quản lý đăng ký thành công")
        else:
            print(f"   ❌ Lỗi truy cập: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return
    
    # Kiểm tra dữ liệu hiện tại
    current_enrollments = Enrollment.objects.filter(student=student)
    enrolled_course_ids = current_enrollments.values_list('course_id', flat=True)
    available_courses = Course.objects.filter(is_active=True).exclude(id__in=enrolled_course_ids)
    
    print(f"\n📊 Trạng thái hiện tại:")
    print(f"   📝 Đã đăng ký: {current_enrollments.count()} môn")
    print(f"   📚 Có thể đăng ký: {available_courses.count()} môn")
    
    # Test đăng ký môn học mới (nếu có)
    if available_courses.exists():
        test_course = available_courses.first()
        print(f"\n➕ Test đăng ký môn học: {test_course.name}")
        
        try:
            response = client.post(reverse('enroll-course', args=[test_course.id]))
            if response.status_code == 302:  # Redirect sau khi thành công
                print("   ✅ Đăng ký môn học thành công")
                
                # Kiểm tra xem đã tạo enrollment chưa
                new_enrollment = Enrollment.objects.filter(student=student, course=test_course).first()
                if new_enrollment:
                    print(f"   ✅ Enrollment đã được tạo: ID {new_enrollment.id}")
                else:
                    print("   ❌ Enrollment không được tạo")
            else:
                print(f"   ❌ Lỗi đăng ký: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Exception khi đăng ký: {e}")
    else:
        print("\n➕ Không có môn học nào để test đăng ký")
    
    # Test hủy đăng ký (chỉ với môn chưa có điểm)
    unenrollable = current_enrollments.filter(grade__isnull=True).first()
    if unenrollable:
        print(f"\n➖ Test hủy đăng ký môn học: {unenrollable.course.name}")
        
        try:
            response = client.post(reverse('unenroll-course', args=[unenrollable.id]))
            if response.status_code == 302:  # Redirect sau khi thành công
                print("   ✅ Hủy đăng ký môn học thành công")
                
                # Kiểm tra xem enrollment đã bị xóa chưa
                still_exists = Enrollment.objects.filter(id=unenrollable.id).exists()
                if not still_exists:
                    print("   ✅ Enrollment đã được xóa")
                else:
                    print("   ❌ Enrollment vẫn còn tồn tại")
            else:
                print(f"   ❌ Lỗi hủy đăng ký: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Exception khi hủy đăng ký: {e}")
    else:
        print("\n➖ Không có môn học nào có thể hủy đăng ký")
    
    client.logout()
    print("\n🚪 Đã đăng xuất")

def check_enrollment_features():
    """Kiểm tra các tính năng đăng ký"""
    print("\n🔍 KIỂM TRA CÁC TÍNH NĂNG ĐĂNG KÝ")
    print("-" * 50)
    
    # Kiểm tra môn học có thể đăng ký
    active_courses = Course.objects.filter(is_active=True).count()
    inactive_courses = Course.objects.filter(is_active=False).count()
    
    print(f"📚 Môn học đang mở: {active_courses}")
    print(f"🔒 Môn học đã đóng: {inactive_courses}")
    
    # Kiểm tra đăng ký có thể hủy
    total_enrollments = Enrollment.objects.count()
    graded_enrollments = Enrollment.objects.filter(grade__isnull=False).count()
    ungraded_enrollments = total_enrollments - graded_enrollments
    
    print(f"📝 Tổng đăng ký: {total_enrollments}")
    print(f"✅ Đã có điểm (không thể hủy): {graded_enrollments}")
    print(f"⏳ Chưa có điểm (có thể hủy): {ungraded_enrollments}")

def print_usage_guide():
    """In hướng dẫn sử dụng"""
    print("\n📝 HƯỚNG DẪN SỬ DỤNG TÍNH NĂNG QUẢN LÝ ĐĂNG KÝ")
    print("=" * 60)
    
    print("🌐 Truy cập: http://127.0.0.1:8000/my-enrollment-management/")
    print("\n🎯 Tính năng có sẵn:")
    print("   ➕ Đăng ký môn học mới:")
    print("      - Nhấn nút ➕ ở cột 'Thao tác' trong bảng 'Môn học có thể đăng ký'")
    print("      - Xác nhận trong hộp thoại")
    print("      - Hệ thống sẽ tự động đăng ký và reload trang")
    
    print("\n   ➖ Hủy đăng ký môn học:")
    print("      - Nhấn nút ❌ ở cột 'Thao tác' trong bảng 'Môn học đã đăng ký'")
    print("      - Chỉ có thể hủy môn học chưa có điểm và trong vòng 30 ngày")
    print("      - Xác nhận trong hộp thoại")
    
    print("\n   👁️ Xem chi tiết môn học:")
    print("      - Nhấn nút 👁️ để xem thông tin chi tiết môn học")
    
    print("\n⚠️  Lưu ý quan trọng:")
    print("   - Chỉ có thể hủy đăng ký môn học chưa có điểm")
    print("   - Thời hạn hủy đăng ký: 30 ngày kể từ ngày đăng ký")
    print("   - Môn học đã đóng không thể đăng ký")
    print("   - Không thể đăng ký trùng lặp môn học")

def main():
    """Hàm chính"""
    try:
        # Test tính năng
        test_enrollment_management()
        
        # Kiểm tra features
        check_enrollment_features()
        
        # Hướng dẫn sử dụng
        print_usage_guide()
        
        print("\n🎉 HOÀN THÀNH TEST TÍNH NĂNG QUẢN LÝ ĐĂNG KÝ!")
        print("=" * 60)
        print("✅ Tính năng quản lý đăng ký đã hoạt động!")
        print("✅ Sinh viên có thể đăng ký và hủy đăng ký môn học!")
        print("✅ Có validation và security checks!")
        print("🌐 Truy cập http://127.0.0.1:8000/login/ để test!")
        
    except Exception as e:
        print(f"❌ Lỗi khi test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
