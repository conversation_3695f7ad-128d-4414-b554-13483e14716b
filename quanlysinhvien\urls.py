"""
URL configuration for quanlysinhvien project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from . import views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', views.home, name='home'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('reports/', views.reports, name='reports'),
    path('students/', include('students.urls')),
    path('courses/', include('courses.urls')),

    # Authentication URLs
    path('login/', auth_views.LoginView.as_view(template_name='auth/login.html'), name='login'),
    path('logout/', views.custom_logout, name='logout'),
    path('register/', views.register_view, name='register'),
    path('password-change/', auth_views.PasswordChangeView.as_view(template_name='auth/password_change.html'), name='password_change'),
    path('password-change/done/', auth_views.PasswordChangeDoneView.as_view(template_name='auth/password_change_done.html'), name='password_change_done'),

    # Student URLs
    path('student-profile/', views.student_profile, name='student-profile'),
    path('my-courses/', views.my_courses, name='my-courses'),
    path('my-courses/all/', views.my_courses_all, name='my-courses-all'),
    path('my-courses/graded/', views.my_courses_graded, name='my-courses-graded'),
    path('my-courses/ungraded/', views.my_courses_ungraded, name='my-courses-ungraded'),
    path('my-enrollment-management/', views.my_enrollment_management, name='my-enrollment-management'),
    path('enroll-course/<int:course_id>/', views.enroll_course, name='enroll-course'),
    path('unenroll-course/<int:enrollment_id>/', views.unenroll_course, name='unenroll-course'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
