#!/usr/bin/env python
"""
Script để test các trang sinh viên
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from courses.models import Enrollment
from django.test import Client
from django.urls import reverse

def test_student_login_and_pages():
    """Test đăng nhập và truy cập các trang sinh viên"""
    print("🔐 TEST ĐĂNG NHẬP VÀ TRUY CẬP TRANG SINH VIÊN")
    print("=" * 60)
    
    # L<PERSON>y một sinh viên để test
    student = Student.objects.first()
    if not student:
        print("❌ Không có sinh viên nào để test")
        return
    
    try:
        user = User.objects.get(email=student.email)
    except User.DoesNotExist:
        print(f"❌ Không tìm thấy user account cho sinh viên {student.full_name()}")
        return
    
    print(f"🎓 Test với sinh viên: {student.full_name()}")
    print(f"📧 Email: {student.email}")
    print(f"👤 Username: {user.username}")
    print(f"🔑 Password: student123")
    
    # Tạo client để test
    client = Client()
    
    # Test đăng nhập
    print("\n🔐 Test đăng nhập...")
    login_response = client.login(username=user.username, password='student123')
    
    if login_response:
        print("   ✅ Đăng nhập thành công!")
        
        # Test trang my-courses
        print("\n📚 Test trang 'Môn học của tôi'...")
        try:
            response = client.get(reverse('my-courses'))
            if response.status_code == 200:
                print("   ✅ Trang my-courses truy cập thành công!")
                print(f"   📄 Status code: {response.status_code}")
            else:
                print(f"   ❌ Lỗi truy cập my-courses: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Exception khi truy cập my-courses: {e}")
        
        # Test trang student-profile
        print("\n👤 Test trang 'Thông tin cá nhân'...")
        try:
            response = client.get(reverse('student-profile'))
            if response.status_code == 200:
                print("   ✅ Trang student-profile truy cập thành công!")
                print(f"   📄 Status code: {response.status_code}")
            else:
                print(f"   ❌ Lỗi truy cập student-profile: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Exception khi truy cập student-profile: {e}")
        
        # Test các trang chi tiết my-courses
        print("\n📋 Test các trang chi tiết my-courses...")
        
        detail_pages = [
            ('my-courses-all', 'Tất cả môn học'),
            ('my-courses-graded', 'Môn học đã có điểm'),
            ('my-courses-ungraded', 'Môn học chưa có điểm'),
            ('my-enrollment-management', 'Quản lý đăng ký')
        ]
        
        for url_name, page_name in detail_pages:
            try:
                response = client.get(reverse(url_name))
                if response.status_code == 200:
                    print(f"   ✅ {page_name}: OK")
                else:
                    print(f"   ❌ {page_name}: Error {response.status_code}")
            except Exception as e:
                print(f"   ❌ {page_name}: Exception {e}")
        
        # Đăng xuất
        client.logout()
        print("\n🚪 Đã đăng xuất")
        
    else:
        print("   ❌ Đăng nhập thất bại!")

def check_student_enrollments():
    """Kiểm tra dữ liệu đăng ký của sinh viên"""
    print("\n📊 KIỂM TRA DỮ LIỆU ĐĂNG KÝ SINH VIÊN")
    print("=" * 50)
    
    student = Student.objects.first()
    if not student:
        print("❌ Không có sinh viên nào")
        return
    
    enrollments = Enrollment.objects.filter(student=student)
    print(f"🎓 Sinh viên: {student.full_name()}")
    print(f"📝 Số môn đã đăng ký: {enrollments.count()}")
    print(f"✅ Số môn có điểm: {enrollments.filter(grade__isnull=False).count()}")
    print(f"⏳ Số môn chưa có điểm: {enrollments.filter(grade__isnull=True).count()}")
    
    if enrollments.exists():
        print("\n📋 Một số môn học đã đăng ký:")
        for enrollment in enrollments[:5]:
            grade_info = f"({enrollment.grade})" if enrollment.grade else "(Chưa có điểm)"
            print(f"   - {enrollment.course.course_code}: {enrollment.course.name} {grade_info}")

def print_login_instructions():
    """In hướng dẫn đăng nhập"""
    print("\n📝 HƯỚNG DẪN ĐĂNG NHẬP CHO SINH VIÊN")
    print("=" * 50)
    
    sample_students = Student.objects.all()[:5]
    
    print("🌐 URL đăng nhập: http://127.0.0.1:8000/login/")
    print("\n👥 Một số tài khoản sinh viên để test:")
    print(f"{'Username':<12} {'Password':<12} {'Họ tên':<20}")
    print("-" * 50)
    
    for student in sample_students:
        print(f"{student.student_id:<12} {'student123':<12} {student.full_name():<20}")
    
    print("\n📋 Sau khi đăng nhập, sinh viên có thể truy cập:")
    print("   📚 Môn học của tôi: /my-courses/")
    print("   👤 Thông tin cá nhân: /student-profile/")
    print("   📊 Tất cả môn học: /my-courses/all/")
    print("   ✅ Môn đã có điểm: /my-courses/graded/")
    print("   ⏳ Môn chưa có điểm: /my-courses/ungraded/")
    print("   ⚙️  Quản lý đăng ký: /my-enrollment-management/")

def main():
    """Hàm chính"""
    print("🎯 TEST CÁC TRANG SINH VIÊN")
    print("=" * 40)
    
    try:
        # Test đăng nhập và các trang
        test_student_login_and_pages()
        
        # Kiểm tra dữ liệu đăng ký
        check_student_enrollments()
        
        # In hướng dẫn
        print_login_instructions()
        
        print("\n🎉 HOÀN THÀNH TEST!")
        print("=" * 40)
        print("✅ Tất cả các trang sinh viên đã sẵn sàng!")
        print("🌐 Truy cập http://127.0.0.1:8000/login/ để đăng nhập")
        
    except Exception as e:
        print(f"❌ Lỗi khi test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
