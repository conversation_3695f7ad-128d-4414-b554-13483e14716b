#!/usr/bin/env python
"""
Script để tạo dữ liệu đăng ký môn học cho sinh viên tiếng Việt mới
"""
import os
import sys
import django
import random
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from students.models import Student
from courses.models import Course, Enrollment
from django.db import transaction

def get_random_grade():
    """Tạo điểm ngẫu nhiên theo phân phối thực tế"""
    rand = random.random()
    if rand < 0.05:  # 5% điểm F
        return random.uniform(0, 3.9), 'F'
    elif rand < 0.15:  # 10% điểm D
        return random.uniform(4.0, 5.4), 'D'
    elif rand < 0.35:  # 20% điểm C
        return random.uniform(5.5, 6.9), 'C'
    elif rand < 0.70:  # 35% điểm B
        return random.uniform(7.0, 8.4), 'B'
    else:  # 30% điểm A
        return random.uniform(8.5, 10.0), 'A'

def create_enrollments_for_students():
    """Tạo dữ liệu đăng ký môn học cho sinh viên"""
    print("📝 Đang tạo dữ liệu đăng ký môn học cho sinh viên tiếng Việt...")
    
    students = Student.objects.all()
    courses = Course.objects.all()
    
    if not students.exists():
        print("❌ Không tìm thấy sinh viên nào!")
        return
    
    if not courses.exists():
        print("❌ Không tìm thấy môn học nào!")
        return
    
    created_count = 0
    total_students = students.count()
    
    for i, student in enumerate(students):
        # Mỗi sinh viên đăng ký 6-12 môn học ngẫu nhiên
        num_courses = random.randint(6, 12)
        selected_courses = random.sample(list(courses), min(num_courses, courses.count()))
        
        for course in selected_courses:
            # Kiểm tra xem đã đăng ký chưa
            if Enrollment.objects.filter(student=student, course=course).exists():
                continue
            
            # Tạo ngày đăng ký hợp lý
            enrollment_date = student.enrollment_date
            if course.start_date > student.enrollment_date:
                enrollment_date = course.start_date
            
            # Thêm random days để tạo sự đa dạng
            enrollment_date += timedelta(days=random.randint(0, 30))
            
            # 75% sinh viên có điểm, 25% chưa có điểm
            has_grade = random.random() < 0.75
            
            if has_grade:
                numeric_grade, letter_grade = get_random_grade()
            else:
                numeric_grade, letter_grade = None, None
            
            try:
                Enrollment.objects.create(
                    student=student,
                    course=course,
                    enrollment_date=enrollment_date,
                    grade=letter_grade,
                    numeric_grade=round(numeric_grade, 1) if numeric_grade else None
                )
                created_count += 1
            except Exception as e:
                print(f"   ⚠️  Lỗi khi tạo enrollment cho {student.full_name()}: {e}")
                continue
        
        if (i + 1) % 10 == 0:
            print(f"   ✓ Đã xử lý {i + 1}/{total_students} sinh viên...")
    
    print(f"✅ Đã tạo {created_count} đăng ký môn học")
    return created_count

def print_enrollment_statistics():
    """In thống kê đăng ký môn học"""
    print("\n📊 THỐNG KÊ ĐĂNG KÝ MÔN HỌC:")
    print("-" * 50)
    
    total_enrollments = Enrollment.objects.count()
    graded_enrollments = Enrollment.objects.filter(grade__isnull=False).count()
    ungraded_enrollments = total_enrollments - graded_enrollments
    
    print(f"   📝 Tổng số đăng ký: {total_enrollments}")
    print(f"   ✅ Đã có điểm: {graded_enrollments}")
    print(f"   ⏳ Chưa có điểm: {ungraded_enrollments}")
    
    if graded_enrollments > 0:
        avg_grade = Enrollment.objects.filter(numeric_grade__isnull=False).aggregate(
            avg=django.db.models.Avg('numeric_grade')
        )['avg']
        print(f"   📈 Điểm trung bình: {avg_grade:.2f}")
    
    # Thống kê theo điểm chữ
    grade_stats = {}
    for grade_choice in ['A', 'B', 'C', 'D', 'F']:
        count = Enrollment.objects.filter(grade=grade_choice).count()
        if count > 0:
            grade_stats[grade_choice] = count
    
    if grade_stats:
        print(f"   📊 Phân bố điểm:")
        for grade, count in grade_stats.items():
            percentage = (count / graded_enrollments) * 100
            print(f"      {grade}: {count} ({percentage:.1f}%)")

def print_sample_enrollments():
    """In một số đăng ký mẫu"""
    print("\n📋 MỘT SỐ ĐĂNG KÝ MẪU:")
    print("-" * 80)
    
    enrollments = Enrollment.objects.select_related('student', 'course')[:10]
    
    for enrollment in enrollments:
        grade_info = f"{enrollment.grade} ({enrollment.numeric_grade})" if enrollment.grade else "Chưa có điểm"
        print(f"   {enrollment.student.full_name()} | {enrollment.course.course_code} - {enrollment.course.name} | {grade_info}")

def main():
    """Hàm chính"""
    print("🎯 TẠO DỮ LIỆU ĐĂNG KÝ MÔN HỌC CHO SINH VIÊN TIẾNG VIỆT")
    print("=" * 70)
    
    try:
        with transaction.atomic():
            # Tạo dữ liệu đăng ký
            created_count = create_enrollments_for_students()
            
            # In thống kê
            print_enrollment_statistics()
            print_sample_enrollments()
            
            print("\n🎉 HOÀN THÀNH TẠO DỮ LIỆU ĐĂNG KÝ!")
            print("=" * 70)
            print("✅ Dữ liệu đăng ký môn học đã được tạo thành công!")
            print("🌐 Bạn có thể truy cập http://127.0.0.1:8000 để xem kết quả")
            
    except Exception as e:
        print(f"❌ Lỗi khi tạo dữ liệu đăng ký: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
