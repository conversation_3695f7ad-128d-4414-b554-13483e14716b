#!/usr/bin/env python
"""
Script để kiểm tra dữ liệu sinh viên tiếng Việt đã tạo
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quanlysinhvien.settings')
django.setup()

from students.models import Student
from courses.models import Course, Enrollment
from django.db.models import Avg, Count

def check_student_names():
    """Kiểm tra tên sinh viên có trùng lặp không"""
    print("🔍 KIỂM TRA TÊN SINH VIÊN")
    print("-" * 50)
    
    students = Student.objects.all()
    total_students = students.count()
    
    # Kiểm tra trùng lặp tên
    full_names = [student.full_name() for student in students]
    unique_names = set(full_names)
    
    print(f"   👥 Tổng số sinh viên: {total_students}")
    print(f"   📝 Số tên duy nhất: {len(unique_names)}")
    
    if len(unique_names) == total_students:
        print("   ✅ Không có tên trùng lặp!")
    else:
        duplicates = total_students - len(unique_names)
        print(f"   ⚠️  Có {duplicates} tên bị trùng lặp")
        
        # Tìm tên trùng lặp
        from collections import Counter
        name_counts = Counter(full_names)
        duplicate_names = [name for name, count in name_counts.items() if count > 1]
        
        print("   📋 Các tên bị trùng lặp:")
        for name in duplicate_names:
            print(f"      - {name} (xuất hiện {name_counts[name]} lần)")

def display_sample_students():
    """Hiển thị danh sách sinh viên mẫu"""
    print("\n📋 DANH SÁCH SINH VIÊN TIẾNG VIỆT")
    print("-" * 80)
    
    students = Student.objects.all()[:20]  # Lấy 20 sinh viên đầu tiên
    
    print(f"{'STT':<4} {'Mã SV':<12} {'Họ và tên':<25} {'Giới tính':<10} {'Email':<30}")
    print("-" * 80)
    
    for i, student in enumerate(students, 1):
        print(f"{i:<4} {student.student_id:<12} {student.full_name():<25} {student.get_gender_display():<10} {student.email:<30}")
    
    if Student.objects.count() > 20:
        print(f"... và {Student.objects.count() - 20} sinh viên khác")

def display_enrollment_stats():
    """Hiển thị thống kê đăng ký môn học"""
    print("\n📊 THỐNG KÊ ĐĂNG KÝ MÔN HỌC")
    print("-" * 50)
    
    total_enrollments = Enrollment.objects.count()
    graded_enrollments = Enrollment.objects.filter(grade__isnull=False).count()
    
    print(f"   📝 Tổng số đăng ký: {total_enrollments}")
    print(f"   ✅ Đã có điểm: {graded_enrollments}")
    print(f"   ⏳ Chưa có điểm: {total_enrollments - graded_enrollments}")
    
    if graded_enrollments > 0:
        avg_grade = Enrollment.objects.filter(numeric_grade__isnull=False).aggregate(
            avg=Avg('numeric_grade')
        )['avg']
        print(f"   📈 Điểm trung bình: {avg_grade:.2f}")

def display_top_students():
    """Hiển thị sinh viên có điểm cao nhất"""
    print("\n🏆 TOP 10 SINH VIÊN CÓ ĐIỂM TRUNG BÌNH CAO NHẤT")
    print("-" * 70)
    
    # Tính điểm trung bình cho từng sinh viên
    students_with_avg = Student.objects.annotate(
        avg_grade=Avg('enrollments__numeric_grade'),
        total_courses=Count('enrollments')
    ).filter(avg_grade__isnull=False).order_by('-avg_grade')[:10]
    
    print(f"{'STT':<4} {'Họ và tên':<25} {'Số môn':<10} {'Điểm TB':<10}")
    print("-" * 70)
    
    for i, student in enumerate(students_with_avg, 1):
        print(f"{i:<4} {student.full_name():<25} {student.total_courses:<10} {student.avg_grade:.2f}")

def display_course_stats():
    """Hiển thị thống kê môn học"""
    print("\n📚 THỐNG KÊ MÔN HỌC")
    print("-" * 50)
    
    total_courses = Course.objects.count()
    active_courses = Course.objects.filter(is_active=True).count()
    
    print(f"   📖 Tổng số môn học: {total_courses}")
    print(f"   ✅ Môn học đang mở: {active_courses}")
    print(f"   ❌ Môn học đã đóng: {total_courses - active_courses}")
    
    # Môn học có nhiều sinh viên đăng ký nhất
    popular_courses = Course.objects.annotate(
        enrollment_count=Count('enrollments')
    ).order_by('-enrollment_count')[:5]
    
    print("\n   🔥 TOP 5 MÔN HỌC ĐƯỢC ĐĂNG KÝ NHIỀU NHẤT:")
    for course in popular_courses:
        print(f"      {course.course_code} - {course.name}: {course.enrollment_count} sinh viên")

def display_gender_stats():
    """Hiển thị thống kê giới tính"""
    print("\n👥 THỐNG KÊ GIỚI TÍNH")
    print("-" * 30)
    
    male_count = Student.objects.filter(gender='M').count()
    female_count = Student.objects.filter(gender='F').count()
    total = Student.objects.count()
    
    if total > 0:
        male_percent = (male_count / total) * 100
        female_percent = (female_count / total) * 100
        
        print(f"   👨 Nam: {male_count} ({male_percent:.1f}%)")
        print(f"   👩 Nữ: {female_count} ({female_percent:.1f}%)")
    else:
        print("   Không có dữ liệu sinh viên")

def main():
    """Hàm chính"""
    print("🎯 KIỂM TRA DỮ LIỆU SINH VIÊN TIẾNG VIỆT")
    print("=" * 60)
    
    try:
        # Kiểm tra tên trùng lặp
        check_student_names()
        
        # Hiển thị sinh viên mẫu
        display_sample_students()
        
        # Thống kê giới tính
        display_gender_stats()
        
        # Thống kê đăng ký
        display_enrollment_stats()
        
        # Top sinh viên
        display_top_students()
        
        # Thống kê môn học
        display_course_stats()
        
        print("\n🎉 KIỂM TRA HOÀN TẤT!")
        print("=" * 60)
        print("✅ Dữ liệu sinh viên tiếng Việt đã được tạo thành công!")
        print("🌐 Truy cập http://127.0.0.1:8000 để xem giao diện web")
        
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra dữ liệu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
